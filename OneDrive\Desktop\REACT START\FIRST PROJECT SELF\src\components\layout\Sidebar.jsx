import { useSelector } from "react-redux";
import { useState } from "react";

import { NavLink } from "react-router-dom";

import {
  Home,
  Users,
  UserPlus,
  BarChart3,
  Settings,
  X,
  Shield,
  Briefcase,
  User,
} from "lucide-react";

const Sidebar = ({ isOpen, onClose }) => {
  const { user, role: roleFromState } = useSelector((state) => state.auth);
  const role = typeof user === "string" ? user : user?.role || roleFromState;

  const getNavigationItems = (role) => {
    const baseItems = [
      { path: "/dashboard", icon: "bx bxs-color bx-spin", label: "Dashboard" },
    ];

    const roleBasedItems = {
      "Secretary-Login": [
        { path: "/users/create", icon: UserPlus, label: "Create User" },
        {
          path: "/form-submission-report",
          icon: "bx bxs-report bx-tada",
          label: "Form Submission Report",
        },
        { path: "/users/list", icon: Users, label: "User Management" },
        { path: "/admin/report", icon: BarChart3, label: "Reports" },
        { path: "/settings", icon: Settings, label: "Settings" },
      ],
      "user-login": [
        { path: "/settings", icon: Settings, label: "Settings" },
        {
          path: "/application-status",
          icon: "bx bxs-printer bx-flashing", // ✅ Boxicon (string)
          label: "Application Status",
        },
        {
          path: "/print-application",
          icon: Briefcase,
          label: "Print Application",
        },
      ],
    };

    return [...baseItems, ...(roleBasedItems[role] || [])];
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case "Secretary-Login":
        return Shield;
      case "user-login":
        return User;
      default:
        return User;
    }
  };

  const getRoleTheme = (role) => {
    switch (role) {
      case "Secretary-Login":
        return {
          background: "from-gray-600 to-gray-800",
          accent: "bg-gray-500",
          hover: "hover:bg-gray-700",
        };
      case "user-login":
        return {
          background: "from-gray-600 to-gray-800",
          accent: "bg-gray-500",
          hover: "hover:bg-gray-700",
        };
      default:
        return {
          background: "from-gray-600 to-gray-800",
          accent: "bg-gray-500",
          hover: "hover:bg-gray-700",
        };
    }
  };

  const theme = getRoleTheme(role);
  const RoleIcon = getRoleIcon(role);
  const navigationItems = getNavigationItems(role); // ✅ pass role here

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={onClose}
        />
      )}
      <link
        href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css"
        rel="stylesheet"
      />

      {/* Sidebar */}
      <div
        className={`
          group fixed top-16 left-0 h-full bg-gradient-to-b ${
            theme.background
          } text-white
          transition-[width] duration-200 ease-in-out z-30
          ${isOpen ? "translate-x-0" : "-translate-x-full"}
          w-16 hover:w-64
        `}
      >
        <div className="p-3">
          {/* Navigation */}
          <nav className="space-y-2">
            {navigationItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) => `
                  flex items-center rounded-lg transition-colors duration-200 px-3 py-2
                  ${
                    isActive
                      ? "bg-white/20 text-white font-semibold"
                      : `text-white/80 ${theme.hover} hover:text-white`
                  }
                `}
                onClick={onClose}
              >
                {/* ✅ Check type of icon */}
                {typeof item.icon === "string" ? (
                  <i className={`${item.icon} text-xl flex-shrink-0`}></i>
                ) : (
                  <item.icon className="w-5 h-5 flex-shrink-0" />
                )}

                <span className="ml-3 whitespace-nowrap overflow-hidden opacity-0 group-hover:opacity-100 group-hover:overflow-visible group-hover:w-auto w-0 transition-all">
                  {item.label}
                </span>
              </NavLink>
            ))}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
