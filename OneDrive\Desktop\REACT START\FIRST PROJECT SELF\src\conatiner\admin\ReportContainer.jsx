import React, { useState } from "react";
import "bootstrap/dist/css/bootstrap.min.css";

const ReportConatiner = () => {
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [selectedStandard, setSelectedStandard] = useState("");
  const [tableData, setTableData] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);

  // Sample districts (you can expand this list)
  const districts = [
    "Patna",
    "Gaya",
    "Muzaffarpur",
    "Bhagalpur",
    "Darbhanga",
    "Araria",
    "Purnia",
    "Madhubani",
    "Saharsa",
    "Sitamarhi",
  ];

  const standards = ["WASTANIA", "FAUQUANIA", "MOULVI", "ALIM", "FAZIL"];

  // Sample table data
  const sampleData = [
    {
      id: 1,
      rollNumber: "RN001",
      candidateName: "<PERSON>",
      fatherName: "<PERSON>",
      district: "Patna",
      standard: "ALIM",
      applicationDate: "2024-01-15",
      status: "Applied",
    },
    {
      id: 2,
      rollNumber: "RN002",
      candidateName: "Fatima Khatun",
      fatherName: "Abdul Rahman",
      district: "Gaya",
      standard: "FAZIL",
      applicationDate: "2024-01-16",
      status: "Verified",
    },
    {
      id: 3,
      rollNumber: "RN003",
      candidateName: "Hassan Sheikh",
      fatherName: "Yusuf Sheikh",
      district: "Muzaffarpur",
      standard: "MOULVI",
      applicationDate: "2024-01-17",
      status: "Pending",
    },
    {
      id: 4,
      rollNumber: "RN004",
      candidateName: "Aisha Begum",
      fatherName: "Ibrahim Khan",
      district: "Bhagalpur",
      standard: "WASTANIA",
      applicationDate: "2024-01-18",
      status: "Applied",
    },
  ];

  const handleSubmit = () => {
    if (!selectedDistrict || !selectedStandard) {
      alert("Please select both District and Standard");
      return;
    }

    // Filter data based on selection or show all sample data
    const filteredData = sampleData.filter(
      (item) =>
        item.district === selectedDistrict && item.standard === selectedStandard
    );

    setTableData(filteredData.length > 0 ? filteredData : sampleData);
  };

  const handleTakeAction = (candidate) => {
    setSelectedCandidate(candidate);
    setShowModal(true);
  };

  const exportToExcel = () => {
    // Simulate export functionality
    alert("Exporting to Excel... Please wait.");
    // In real implementation, you would integrate with a library like xlsx
  };

  return (
    <div className="container-fluid mt-4">
      {/* Preloader simulation */}

      {/* Header */}
      <div className="row mb-4">
        <div className="col-sm-6">
          <h5
            className="text-dark"
            style={{ fontSize: "28px", fontWeight: "bold" }}
          >
            District Wise Admin Report Of Applied Candidates:
          </h5>
        </div>
        <div className="col-sm-6">
          <nav aria-label="breadcrumb">
            <ol className="breadcrumb justify-content-end">
              <li className="breadcrumb-item">
                <a href="#" className="text-decoration-none">
                  Signout
                </a>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="row">
        <div className="col-12">
          <div className="card border-success">
            <div className="card-header bg-success text-white">
              <h6 className="card-title fw-bold mb-0">
                Choose District And Get Report Of Applied Candidates
              </h6>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-12">
                  <div className="card border-primary">
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <label
                            htmlFor="district"
                            className="form-label text-danger fw-bold"
                          >
                            Choose your District:
                          </label>
                          <select
                            id="district"
                            className="form-select"
                            value={selectedDistrict}
                            onChange={(e) =>
                              setSelectedDistrict(e.target.value)
                            }
                            required
                          >
                            <option value="">Select District</option>
                            {districts.map((district, index) => (
                              <option key={index} value={district}>
                                {district}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="col-md-6">
                          <label
                            htmlFor="standard"
                            className="form-label text-danger fw-bold"
                          >
                            Choose your Standard:
                          </label>
                          <select
                            id="standard"
                            className="form-select"
                            value={selectedStandard}
                            onChange={(e) =>
                              setSelectedStandard(e.target.value)
                            }
                            required
                          >
                            <option value="">Select Standard</option>
                            {standards.map((standard, index) => (
                              <option key={index} value={standard}>
                                {standard}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <div className="card-footer bg-transparent">
                        <p className="text-danger fw-bold mb-2">
                          Note: Please Wait When You Click On Export to Excel
                          Button It Takes Some Time.
                        </p>
                        <button
                          type="button"
                          className="btn btn-primary float-end"
                          onClick={handleSubmit}
                        >
                          Submit
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table Section */}
      {tableData.length > 0 && (
        <div className="row mt-4">
          <div className="col-12">
            <div className="card border-primary">
              <div className="card-body">
                <div className="row mb-3">
                  <div className="col-12">
                    <button
                      type="button"
                      className="btn btn-success"
                      onClick={exportToExcel}
                    >
                      Export to Excel
                    </button>
                  </div>
                </div>
                <div className="table-responsive">
                  <table className="table table-bordered table-striped">
                    <thead className="table-dark">
                      <tr>
                        <th>S.No</th>
                        <th>Roll Number</th>
                        <th>Candidate Name</th>
                        <th>Father's Name</th>
                        <th>District</th>
                        <th>Standard</th>
                        <th>Application Date</th>
                        <th>Status</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.map((candidate, index) => (
                        <tr key={candidate.id}>
                          <td>{index + 1}</td>
                          <td>{candidate.rollNumber}</td>
                          <td>{candidate.candidateName}</td>
                          <td>{candidate.fatherName}</td>
                          <td>{candidate.district}</td>
                          <td>{candidate.standard}</td>
                          <td>{candidate.applicationDate}</td>
                          <td>
                            <span
                              className={`badge ${
                                candidate.status === "Verified"
                                  ? "bg-success"
                                  : candidate.status === "Pending"
                                  ? "bg-warning"
                                  : "bg-primary"
                              }`}
                            >
                              {candidate.status}
                            </span>
                          </td>
                          <td>
                            <button
                              className="btn btn-sm btn-outline-primary"
                              onClick={() => handleTakeAction(candidate)}
                            >
                              Take Action
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div
          className="modal fade show d-block"
          tabIndex="-1"
          style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog modal-xl">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Verify Candidate Details</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowModal(false)}
                ></button>
              </div>
              <div className="modal-body" style={{ height: "70vh" }}>
                {selectedCandidate && (
                  <div className="container-fluid">
                    <h6 className="mb-3">Candidate Information:</h6>
                    <div className="row">
                      <div className="col-md-6">
                        <p>
                          <strong>Roll Number:</strong>{" "}
                          {selectedCandidate.rollNumber}
                        </p>
                        <p>
                          <strong>Name:</strong>{" "}
                          {selectedCandidate.candidateName}
                        </p>
                        <p>
                          <strong>Father's Name:</strong>{" "}
                          {selectedCandidate.fatherName}
                        </p>
                        <p>
                          <strong>District:</strong>{" "}
                          {selectedCandidate.district}
                        </p>
                      </div>
                      <div className="col-md-6">
                        <p>
                          <strong>Standard:</strong>{" "}
                          {selectedCandidate.standard}
                        </p>
                        <p>
                          <strong>Application Date:</strong>{" "}
                          {selectedCandidate.applicationDate}
                        </p>
                        <p>
                          <strong>Current Status:</strong>
                          <span
                            className={`badge ms-2 ${
                              selectedCandidate.status === "Verified"
                                ? "bg-success"
                                : selectedCandidate.status === "Pending"
                                ? "bg-warning"
                                : "bg-primary"
                            }`}
                          >
                            {selectedCandidate.status}
                          </span>
                        </p>
                      </div>
                    </div>
                    <hr />
                    <div className="row mt-4">
                      <div className="col-12">
                        <h6>Actions Available:</h6>
                        <button className="btn btn-success me-2">Verify</button>
                        <button className="btn btn-warning me-2">
                          Mark Pending
                        </button>
                        <button className="btn btn-danger me-2">Reject</button>
                        <button className="btn btn-info">
                          Send Notification
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowModal(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportConatiner;
